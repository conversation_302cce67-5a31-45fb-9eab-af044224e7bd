from datetime import datetime
from typing import Literal
from uuid import UUID

from pydantic import BaseModel


class MessageModel(BaseModel):
    role: str
    content: str


class FrontendToolCallResult(BaseModel):
    action: Literal["continue", "abort"]


class FrontendToolCall(BaseModel):
    tool_name: str
    result: FrontendToolCallResult


class ChatRequest(BaseModel):
    content: str
    role: Literal["user", "assistant"]
    tool_calls: list[FrontendToolCall] = []
    thread_id: str
    crm_account_id: str | None = None


class TokenUsageDetails(BaseModel):
    accepted_prediction_tokens: int = 0
    audio_tokens: int = 0
    reasoning_tokens: int = 0
    rejected_prediction_tokens: int = 0


class PromptTokenDetails(BaseModel):
    audio_tokens: int = 0
    cached_tokens: int = 0


class TokenUsage(BaseModel):
    completion_tokens: int
    prompt_tokens: int
    total_tokens: int
    completion_tokens_details: TokenUsageDetails
    prompt_tokens_details: PromptTokenDetails


class LogProbItem(BaseModel):
    token: str
    bytes: list[int]
    logprob: float
    top_logprobs: list[dict]


class LogProbs(BaseModel):
    content: list[LogProbItem]


class ResponseMetadata(BaseModel):
    token_usage: TokenUsage
    model_name: str
    system_fingerprint: str
    id: str
    finish_reason: str
    logprobs: LogProbs | None = None


class ChatResponse(BaseModel):
    response: str
    metadata: ResponseMetadata
    thread_id: str


class ThreadRead(BaseModel):
    id: str
    organization_member_id: UUID
    crm_account_id: str | None
    created_at: datetime
    updated_at: datetime


class ThreadsRead(BaseModel):
    threads: list[ThreadRead]


class ThreadMessage(BaseModel):
    id: str
    role: Literal["user", "assistant"]
    content: str


class PaginationInfo(BaseModel):
    thread_id: str
    current_page: int
    page_size: int
    total_messages: int
    total_pages: int


class ThreadHistoryResponse(BaseModel):
    pagination: PaginationInfo
    messages: list[ThreadMessage]
