import uuid

from sqlalchemy import Foreign<PERSON>ey, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import BaseModel
from app.workspace.models import (
    Environment,
    OrganizationMember,
)


class OrganizationMemberThread(BaseModel):
    __tablename__ = "organization_member_threads"

    thread_id: Mapped[str] = mapped_column(
        Text, nullable=False, index=True, unique=True
    )
    organization_member_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization_member.id"),
        nullable=False,
        index=True,
    )
    environment_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("environment.id"),
        nullable=False,
    )
    crm_account_id: Mapped[str | None] = mapped_column(String(50), nullable=True)

    organization_member: Mapped[OrganizationMember] = relationship("OrganizationMember")
    environment: Mapped[Environment] = relationship("Environment")

    def __repr__(self):
        return (
            f"<OrganizationMemberThread(id='{self.id}', thread_id='{self.thread_id}')>"
        )

    __table_args__ = (
        UniqueConstraint(
            "thread_id",
            "organization_member_id",
            "environment_id",
            name="uq_thread_organization_member_environment",
        ),
    )
