import datetime
from typing import Any, Optional

from pydantic import BaseModel, Field


class CalendarEventAttendee(BaseModel):
    email: str
    name: Optional[str] = None
    response_status: Optional[str] = None  # "accepted", "declined", "tentative", "needsAction"
    is_organizer: bool = False
    is_optional: bool = False


class CalendarEventDateTime(BaseModel):
    datetime: Optional[datetime.datetime] = None
    date: Optional[datetime.date] = None
    timezone: Optional[str] = None


class CalendarEventRecurrence(BaseModel):
    frequency: str  # "daily", "weekly", "monthly", "yearly"
    interval: int = 1
    count: Optional[int] = None
    until: Optional[datetime.datetime] = None
    by_day: Optional[list[str]] = None  # ["MO", "TU", "WE", "TH", "FR", "SA", "SU"]
    by_month_day: Optional[list[int]] = None
    by_month: Optional[list[int]] = None


class CalendarEvent(BaseModel):
    id: str
    calendar_id: str
    title: str
    description: Optional[str] = None
    location: Optional[str] = None
    start: CalendarEventDateTime
    end: CalendarEventDateTime
    all_day: bool = False
    attendees: list[CalendarEventAttendee] = []
    organizer: Optional[CalendarEventAttendee] = None
    recurrence: Optional[CalendarEventRecurrence] = None
    status: str = "confirmed"  # "confirmed", "tentative", "cancelled"
    visibility: str = "default"  # "default", "public", "private", "confidential"
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None
    html_link: Optional[str] = None
    meeting_url: Optional[str] = None
    raw_data: Optional[dict[str, Any]] = None


class Calendar(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    timezone: Optional[str] = None
    is_primary: bool = False
    access_role: str = "reader"  # "owner", "reader", "writer", "freeBusyReader"
    color_id: Optional[str] = None
    background_color: Optional[str] = None
    foreground_color: Optional[str] = None
    raw_data: Optional[dict[str, Any]] = None


class CalendarEventCreateData(BaseModel):
    calendar_id: str
    title: str
    description: Optional[str] = None
    location: Optional[str] = None
    start: CalendarEventDateTime
    end: CalendarEventDateTime
    all_day: bool = False
    attendees: list[CalendarEventAttendee] = []
    recurrence: Optional[CalendarEventRecurrence] = None
    visibility: str = "default"
    send_notifications: bool = True


class CalendarEventUpdateData(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    start: Optional[CalendarEventDateTime] = None
    end: Optional[CalendarEventDateTime] = None
    all_day: Optional[bool] = None
    attendees: Optional[list[CalendarEventAttendee]] = None
    recurrence: Optional[CalendarEventRecurrence] = None
    status: Optional[str] = None
    visibility: Optional[str] = None
    send_notifications: bool = True


class CalendarEventListParams(BaseModel):
    calendar_id: str
    start_time: Optional[datetime.datetime] = None
    end_time: Optional[datetime.datetime] = None
    max_results: int = Field(default=250, le=2500)
    single_events: bool = True
    order_by: str = "startTime"  # "startTime", "updated"
    show_deleted: bool = False


class CalendarListSlice(BaseModel):
    calendars: list[Calendar]
    next_page_token: Optional[str] = None


class CalendarEventListSlice(BaseModel):
    calendar_id: str
    events: list[CalendarEvent]
    next_page_token: Optional[str] = None
    start_time: Optional[datetime.datetime] = None
    end_time: Optional[datetime.datetime] = None


class FreeBusyRequest(BaseModel):
    calendar_ids: list[str]
    start_time: datetime.datetime
    end_time: datetime.datetime
    timezone: Optional[str] = None


class FreeBusyPeriod(BaseModel):
    start: datetime.datetime
    end: datetime.datetime


class FreeBusyCalendar(BaseModel):
    calendar_id: str
    busy_periods: list[FreeBusyPeriod]
    errors: list[dict[str, Any]] = []


class FreeBusyResponse(BaseModel):
    calendars: list[FreeBusyCalendar]
    start_time: datetime.datetime
    end_time: datetime.datetime
