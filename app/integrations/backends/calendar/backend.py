import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.base.calendar_adapter import BaseCalendarAdapter
from app.integrations.base.calendar_backend import BaseCalendarBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.schemas import Calendar, CalendarEvent, CalendarEventDateTime

logger = get_logger()


class CalendarBackend(BaseCalendarBackend):
    """Calendar backend implementation providing business logic for calendar operations."""

    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseCalendarAdapter],
        source: str,
    ):
        super().__init__(context, adapter_class, source)

    async def _get_adapter(self) -> BaseCalendarAdapter:
        """Get an instance of the calendar adapter with credentials."""
        credentials_resolver: ICredentialsResolver = self.context.credentials_resolver
        credentials = await credentials_resolver.get_credentials(self.source)
        return self.adapter_class(credentials)

    async def list_calendars(self) -> list[Calendar]:
        """List all calendars accessible to the user."""
        adapter = await self._get_adapter()
        return await adapter.list_calendars()

    async def get_calendar(self, calendar_id: str) -> Calendar:
        """Get a specific calendar by ID."""
        adapter = await self._get_adapter()
        return await adapter.get_calendar(calendar_id)

    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        """List events from a calendar."""
        adapter = await self._get_adapter()
        return await adapter.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            single_events=single_events,
            order_by=order_by,
            show_deleted=show_deleted,
            page_token=page_token,
        )

    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        """Get a specific event by ID."""
        adapter = await self._get_adapter()
        return await adapter.get_event(calendar_id, event_id)

    async def create_event(
        self,
        calendar_id: str,
        title: str,
        start: CalendarEventDateTime,
        end: CalendarEventDateTime,
        description: str | None = None,
        location: str | None = None,
        attendees: list[dict[str, Any]] | None = None,
        all_day: bool = False,
        send_notifications: bool = True,
        **kwargs: Any,
    ) -> CalendarEvent:
        """Create a new event in the calendar."""
        adapter = await self._get_adapter()

        # Log the event creation for audit purposes
        logger.info(
            f"Creating calendar event: {title} in calendar {calendar_id} "
            f"from {start} to {end}"
        )

        return await adapter.create_event(
            calendar_id=calendar_id,
            title=title,
            start=start,
            end=end,
            description=description,
            location=location,
            attendees=attendees,
            all_day=all_day,
            send_notifications=send_notifications,
            **kwargs,
        )

    async def update_event(
        self,
        calendar_id: str,
        event_id: str,
        title: str | None = None,
        start: CalendarEventDateTime | None = None,
        end: CalendarEventDateTime | None = None,
        description: str | None = None,
        location: str | None = None,
        attendees: list[dict[str, Any]] | None = None,
        all_day: bool | None = None,
        status: str | None = None,
        send_notifications: bool = True,
        **kwargs: Any,
    ) -> CalendarEvent:
        """Update an existing event."""
        adapter = await self._get_adapter()

        # Log the event update for audit purposes
        logger.info(f"Updating calendar event {event_id} in calendar {calendar_id}")

        return await adapter.update_event(
            calendar_id=calendar_id,
            event_id=event_id,
            title=title,
            start=start,
            end=end,
            description=description,
            location=location,
            attendees=attendees,
            all_day=all_day,
            status=status,
            send_notifications=send_notifications,
            **kwargs,
        )

    async def delete_event(
        self,
        calendar_id: str,
        event_id: str,
        send_notifications: bool = True,
    ) -> bool:
        """Delete an event from the calendar."""
        adapter = await self._get_adapter()

        # Log the event deletion for audit purposes
        logger.info(f"Deleting calendar event {event_id} from calendar {calendar_id}")

        return await adapter.delete_event(
            calendar_id=calendar_id,
            event_id=event_id,
            send_notifications=send_notifications,
        )

    async def get_free_busy(
        self,
        calendar_ids: list[str],
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        """Get free/busy information for calendars."""
        adapter = await self._get_adapter()
        return await adapter.get_free_busy(
            calendar_ids=calendar_ids,
            start_time=start_time,
            end_time=end_time,
            timezone=timezone,
        )

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[CalendarEvent]:
        """Search for events in a calendar."""
        adapter = await self._get_adapter()
        return await adapter.search_events(
            calendar_id=calendar_id,
            query=query,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            order_by=order_by,
        )

    async def get_user_info(self) -> dict[str, Any]:
        """Get information about the authenticated user."""
        adapter = await self._get_adapter()
        return await adapter.get_user_info()

    async def get_upcoming_events(
        self,
        calendar_id: str,
        days_ahead: int = 7,
        max_results: int = 50,
    ) -> list[CalendarEvent]:
        """Get upcoming events for the next N days."""
        now = datetime.datetime.now()
        end_time = now + datetime.timedelta(days=days_ahead)

        events_response = await self.list_events(
            calendar_id=calendar_id,
            start_time=now,
            end_time=end_time,
            max_results=max_results,
            single_events=True,
            order_by="startTime",
        )

        return events_response["events"]

    async def get_events_for_date(
        self,
        calendar_id: str,
        date: datetime.date,
    ) -> list[CalendarEvent]:
        """Get all events for a specific date."""
        start_time = datetime.datetime.combine(date, datetime.time.min)
        end_time = datetime.datetime.combine(date, datetime.time.max)

        events_response = await self.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            single_events=True,
            order_by="startTime",
        )

        return events_response["events"]

    async def check_availability(
        self,
        calendar_ids: list[str],
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        timezone: str | None = None,
    ) -> dict[str, bool]:
        """Check if calendars are available during a time period."""
        free_busy_response = await self.get_free_busy(
            calendar_ids=calendar_ids,
            start_time=start_time,
            end_time=end_time,
            timezone=timezone,
        )

        availability = {}
        for calendar_info in free_busy_response["calendars"]:
            calendar_id = calendar_info["calendar_id"]
            busy_periods = calendar_info["busy_periods"]

            # Check if the requested time overlaps with any busy period
            is_available = True
            for busy_period in busy_periods:
                busy_start = busy_period["start"]
                busy_end = busy_period["end"]

                # Check for overlap
                if start_time < busy_end and end_time > busy_start:
                    is_available = False
                    break

            availability[calendar_id] = is_available

        return availability
