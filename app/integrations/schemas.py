import datetime

from pydantic import BaseModel


class MessageData(BaseModel):
    message_id: str
    channel_id: str
    content: str
    sent_at: datetime.datetime
    last_edit_at: datetime.datetime | None = None
    tombstone: bool | None = False
    author: str | None = None
    thread_id: str | None = None
    parent_id: str | None = None


class MessageChangelogData(BaseModel):
    channel_id: str
    operation: str
    message_id: str
    cursor_id: int
    created_at: datetime.datetime


class ChannelDataSlice(BaseModel):
    channel_id: str
    messages: list[MessageData]
    from_time: datetime.datetime
    to_time: datetime.datetime


class ReconciliationStats(BaseModel):
    inserts: int
    updates: int
    deletes: int


class ChannelIngestionResult(BaseModel):
    messages_count: int
    inserts: int
    updates: int
    deletes: int
    from_time: datetime.datetime
    to_time: datetime.datetime


class ChannelProcessingResult(BaseModel):
    processed_changes: int
    regenerated_documents: int
    deleted_documents: int


class DocumentData(BaseModel):
    id: str
    content: str
    source_timestamp: datetime.datetime
    tags: set[str]


class CRMAccountAccessData(BaseModel):
    account_id: str
    account_name: str
    access_type: str
    access_role: str | None


class CRMAccountAccessSlice(BaseModel):
    user_id: str
    accounts: list[CRMAccountAccessData]


class CRMAccountAccessSyncResult(BaseModel):
    new_access_count: int
    old_access_count: int


class FileData(BaseModel):
    id: str
    name: str
    size: int
    time_created: datetime.datetime
    last_modified: datetime.datetime
    md5_hash: str
    content_type: str


class FileProcessingResult(BaseModel):
    processed_files: int
    deleted_documents: int


# Calendar schemas
class CalendarEventAttendee(BaseModel):
    email: str
    name: str | None = None
    response_status: str | None = (
        None  # "accepted", "declined", "tentative", "needsAction"
    )
    is_organizer: bool = False
    is_optional: bool = False


class CalendarEventDateTime(BaseModel):
    dt: datetime.datetime | None = None
    date: datetime.date | None = None
    timezone: str | None = None


class CalendarEventRecurrence(BaseModel):
    frequency: str  # "daily", "weekly", "monthly", "yearly"
    interval: int = 1
    count: int | None = None
    until: datetime.datetime | None = None
    by_day: list[str] | None = None  # ["MO", "TU", "WE", "TH", "FR", "SA", "SU"]
    by_month_day: list[int] | None = None
    by_month: list[int] | None = None


class CalendarEvent(BaseModel):
    id: str
    calendar_id: str
    title: str
    description: str | None = None
    location: str | None = None
    start: CalendarEventDateTime
    end: CalendarEventDateTime
    all_day: bool = False
    attendees: list[CalendarEventAttendee] = []
    organizer: CalendarEventAttendee | None = None
    recurrence: CalendarEventRecurrence | None = None
    status: str = "confirmed"  # "confirmed", "tentative", "cancelled"
    visibility: str = "default"  # "default", "public", "private", "confidential"
    created_at: datetime.datetime | None = None
    updated_at: datetime.datetime | None = None
    html_link: str | None = None
    meeting_url: str | None = None


class Calendar(BaseModel):
    id: str
    name: str
    description: str | None = None
    timezone: str | None = None
    is_primary: bool = False
    access_role: str = "reader"  # "owner", "reader", "writer", "freeBusyReader"
    color_id: str | None = None
    background_color: str | None = None
    foreground_color: str | None = None
