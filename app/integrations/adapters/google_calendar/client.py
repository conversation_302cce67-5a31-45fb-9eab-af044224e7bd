from typing import Any

from google.auth.credentials import Credentials
from google.oauth2.credentials import Credentials as OAuth2Credentials
from googleapiclient.discovery import build

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async

logger = get_logger()


class GoogleCalendarClientError(Exception):
    pass


class GoogleCalendarClient:
    def __init__(self, credentials: dict[str, Any]):
        self._credentials = self._create_credentials(credentials)
        self._service = self._build_service()

    def _create_credentials(self, credentials: dict[str, Any]) -> Credentials:
        try:
            return OAuth2Credentials(
                token=credentials.get("access_token"),
                refresh_token=credentials.get("refresh_token"),
                token_uri=credentials.get(
                    "token_uri", "https://oauth2.googleapis.com/token"
                ),
                client_id=credentials.get("client_id"),
                client_secret=credentials.get("client_secret"),
                scopes=credentials.get(
                    "scopes", ["https://www.googleapis.com/auth/calendar"]
                ),
            )
        except Exception as e:
            raise GoogleCalendarClientError(
                f"Failed to create credentials: {str(e)}"
            ) from e

    def _build_service(self):
        try:
            return build("calendar", "v3", credentials=self._credentials)
        except Exception as e:
            raise GoogleCalendarClientError(
                f"Failed to build Calendar service: {str(e)}"
            ) from e

    @to_async
    def list_calendars(self) -> dict[str, Any]:
        return self._service.calendarList().list().execute()

    @to_async
    def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        return self._service.calendarList().get(calendarId=calendar_id).execute()

    @to_async
    def list_events(
        self,
        calendar_id: str,
        time_min: str | None = None,
        time_max: str | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        params = {
            "calendarId": calendar_id,
            "maxResults": max_results,
            "singleEvents": single_events,
            "orderBy": order_by,
            "showDeleted": show_deleted,
        }

        if time_min:
            params["timeMin"] = time_min
        if time_max:
            params["timeMax"] = time_max
        if page_token:
            params["pageToken"] = page_token

        return self._service.events().list(**params).execute()

    @to_async
    def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        return (
            self._service.events()
            .get(calendarId=calendar_id, eventId=event_id)
            .execute()
        )

    @to_async
    def create_event(
        self,
        calendar_id: str,
        event_data: dict[str, Any],
        send_notifications: bool = True,
    ) -> dict[str, Any]:
        return (
            self._service.events()
            .insert(
                calendarId=calendar_id,
                body=event_data,
                sendNotifications=send_notifications,
            )
            .execute()
        )

    @to_async
    def update_event(
        self,
        calendar_id: str,
        event_id: str,
        event_data: dict[str, Any],
        send_notifications: bool = True,
    ) -> dict[str, Any]:
        return (
            self._service.events()
            .update(
                calendarId=calendar_id,
                eventId=event_id,
                body=event_data,
                sendNotifications=send_notifications,
            )
            .execute()
        )

    @to_async
    def delete_event(
        self,
        calendar_id: str,
        event_id: str,
        send_notifications: bool = True,
    ) -> None:
        self._service.events().delete(
            calendarId=calendar_id,
            eventId=event_id,
            sendNotifications=send_notifications,
        ).execute()

    @to_async
    def get_free_busy(
        self,
        calendar_ids: list[str],
        time_min: str,
        time_max: str,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        body = {
            "timeMin": time_min,
            "timeMax": time_max,
            "items": [{"id": cal_id} for cal_id in calendar_ids],
        }

        if timezone:
            body["timeZone"] = timezone

        return self._service.freebusy().query(body=body).execute()

    @to_async
    def get_user_info(self) -> dict[str, Any]:
        return self._service.calendarList().get(calendarId="primary").execute()
