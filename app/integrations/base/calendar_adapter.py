import datetime
from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import BaseAdapter
from app.integrations.schemas import (
    Calendar,
    CalendarEvent,
    CalendarEventDateTime,
)


class BaseCalendarAdapter(BaseAdapter, ABC):
    """Base adapter for calendar integrations."""

    @abstractmethod
    async def list_calendars(self) -> list[Calendar]:
        """List all calendars accessible to the user."""
        pass

    @abstractmethod
    async def get_calendar(self, calendar_id: str) -> Calendar:
        """Get a specific calendar by ID."""
        pass

    @abstractmethod
    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        """
        List events from a calendar.

        Returns a dict with:
        - events: list[CalendarEvent]
        - next_page_token: str | None
        """
        pass

    @abstractmethod
    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        """Get a specific event by ID."""
        pass

    @abstractmethod
    async def create_event(
        self,
        calendar_id: str,
        title: str,
        start: CalendarEventDateTime,
        end: CalendarEventDateTime,
        description: str | None = None,
        location: str | None = None,
        attendees: list[dict[str, Any]] | None = None,
        all_day: bool = False,
        send_notifications: bool = True,
        **kwargs: Any,
    ) -> CalendarEvent:
        """Create a new event in the calendar."""
        pass

    @abstractmethod
    async def update_event(
        self,
        calendar_id: str,
        event_id: str,
        title: str | None = None,
        start: CalendarEventDateTime | None = None,
        end: CalendarEventDateTime | None = None,
        description: str | None = None,
        location: str | None = None,
        attendees: list[dict[str, Any]] | None = None,
        all_day: bool | None = None,
        status: str | None = None,
        send_notifications: bool = True,
        **kwargs: Any,
    ) -> CalendarEvent:
        """Update an existing event."""
        pass

    @abstractmethod
    async def delete_event(
        self,
        calendar_id: str,
        event_id: str,
        send_notifications: bool = True,
    ) -> bool:
        """Delete an event from the calendar."""
        pass

    @abstractmethod
    async def get_free_busy(
        self,
        calendar_ids: list[str],
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        """
        Get free/busy information for calendars.

        Returns a dict with:
        - calendars: list of calendar free/busy info
        - start_time: datetime
        - end_time: datetime
        """
        pass

    @abstractmethod
    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[CalendarEvent]:
        """Search for events in a calendar."""
        pass

    @abstractmethod
    async def get_user_info(self) -> dict[str, Any]:
        """Get information about the authenticated user."""
        pass
